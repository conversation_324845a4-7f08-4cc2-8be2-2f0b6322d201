"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Toaster } from "@/components/ui/toaster";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

// Types for Linktree extraction
interface LinktreeLink {
  title?: string;
  url: string;
  icon?: string;
}

interface LinktreeExtraInfo {
  location?: string;
  username?: string;
  description?: string;
  avatar_url?: string;
  user_bio?: string;
}

interface LinktreeData {
  extra_info?: LinktreeExtraInfo;
  links: LinktreeLink[];
}

interface LinktreeExtractResult {
  data?: LinktreeData[];
  success?: boolean;
  error?: string;
}

export default function LinktreeExtractorPage() {
  const [linktreeUrl, setLinktreeUrl] = useState("");
  const [additionalInfo, setAdditionalInfo] = useState("");
  const [autoExtract, setAutoExtract] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [apiResponse, setApiResponse] = useState<LinktreeExtractResult | null>(
    null
  );
  const { toast } = useToast();

  const handleExtract = async () => {
    setIsLoading(true);
    setError(null);
    setApiResponse(null);
    try {
      const response = await fetch("/api/linktree", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ url: linktreeUrl }),
      });

      if (!response.ok) {
        throw new Error("Failed to extract data");
      }

      const data = await response.json();
      // Handle FireCrawl response structure
      let extractedData;

      if (data.success && data.data && data.data.length > 0) {
        // If we have successful data, use the first item
        extractedData = data.data[0];
      } else if (data.data && data.data.length > 0) {
        // Fallback to first data item
        extractedData = data.data[0];
      } else if (data.success === false && data.error) {
        // Handle error case
        extractedData = { error: data.error };
      } else {
        // Show the full response for debugging
        extractedData = data;
      }

      setAdditionalInfo(JSON.stringify(extractedData, null, 2));
      setApiResponse(data);
      toast({
        title: "Success",
        description: "Linktree data extracted successfully.",
      });
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (autoExtract) {
      await handleExtract();
    }

    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(
        "https://side-projects-linktree-extractor.vhhb1z.easypanel.host/api/v1/scrape/process",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            user: linktreeUrl,
            info: additionalInfo,
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to submit to external API");
      }

      toast({
        title: "Success",
        description: "Data submitted successfully.",
      });
      console.log("Successfully submitted to external API");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 md:p-8">
      <Toaster />
      <h1 className="text-2xl sm:text-3xl font-bold mb-4">
        Linktree Extractor
      </h1>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Input</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="linktree-url">Linktree URL/Username</Label>
                <Input
                  id="linktree-url"
                  value={linktreeUrl}
                  onChange={(e) => setLinktreeUrl(e.target.value)}
                  placeholder="e.g., https://linktr.ee/yourname or yourname"
                />
              </div>
              <div>
                <Label htmlFor="additional-info">Additional Info</Label>
                <Textarea
                  id="additional-info"
                  value={additionalInfo}
                  onChange={(e) => setAdditionalInfo(e.target.value)}
                  placeholder="Optional additional information"
                  rows={10}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="auto-extract"
                  checked={autoExtract}
                  onCheckedChange={setAutoExtract}
                />
                <Label htmlFor="auto-extract">
                  Automatically extract on submit
                </Label>
              </div>
              {error && <p className="text-red-500">{error}</p>}
              <div className="flex flex-wrap gap-2">
                <Button onClick={handleExtract} disabled={isLoading}>
                  {isLoading ? <LoadingSpinner /> : "Extract Info"}
                </Button>
                <Button onClick={handleSubmit} disabled={isLoading}>
                  {isLoading ? <LoadingSpinner /> : "Submit"}
                </Button>
                <Button
                  onClick={() =>
                    setAdditionalInfo(
                      "URL: | \nUSERNAME: | \nDESCRIÇÃO/BIO: | \nLOCALIZAÇÃO: | \nINSTAGRAM: | \nEQUIPE: | \nOGIMAGE: | \nBGIMAGE: \nREVIEW 1: | \nREVIEW 2: | \nREVIEW 3: | \nREVIEW 4: | \nREVIEW 5: |"
                    )
                  }
                  disabled={isLoading}
                  variant="outline"
                >
                  Insert Pre-defined Text
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
        <div>
          <Card>
            <CardHeader>
              <CardTitle>API Response</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading && (
                <div className="flex justify-center items-center">
                  <LoadingSpinner />
                </div>
              )}
              {apiResponse && (
                <pre className="p-4 bg-gray-100 dark:bg-gray-800 rounded-md overflow-x-auto text-sm">
                  {JSON.stringify(apiResponse, null, 2)}
                </pre>
              )}
              {!isLoading && !apiResponse && (
                <p className="text-gray-500 dark:text-gray-400">
                  The API response will be displayed here.
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Toaster } from "@/components/ui/toaster";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormField,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";

// Types for Linktree extraction
interface LinktreeLink {
  title?: string;
  url: string;
  icon?: string;
}

interface LinktreeExtraInfo {
  location?: string;
  username?: string;
  description?: string;
  avatar_url?: string;
  user_bio?: string;
}

interface LinktreeData {
  extra_info?: LinktreeExtraInfo;
  links: LinktreeLink[];
}

interface LinktreeExtractResult {
  data?: LinktreeData[];
  success?: boolean;
  error?: string;
}

// Template options for the API
const templateOptions = [
  {
    value: "general",
    label: "General",
    description: "Default template for any type of business or profile",
  },
  {
    value: "tattoo",
    label: "Tattoo Studio",
    description:
      "Specialized for tattoo studios, tattoo artists and body art professionals",
  },
  {
    value: "barber",
    label: "Barber Shop",
    description:
      "Specialized for barber shops, barbers and men's beauty salons",
  },
  {
    value: "transform",
    label: "Transform",
    description: "Original/legacy template for basic data transformation",
  },
];

export default function LinktreeExtractorPage() {
  const [linktreeUrl, setLinktreeUrl] = useState("");
  const [additionalInfo, setAdditionalInfo] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState("general");
  const [autoExtract, setAutoExtract] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<{
    [key: string]: string;
  }>({});
  const [apiResponse, setApiResponse] = useState<LinktreeExtractResult | null>(
    null
  );
  const { toast } = useToast();

  // Form validation function
  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!linktreeUrl.trim()) {
      errors.linktreeUrl = "Linktree URL or username is required";
    } else if (linktreeUrl.trim().length < 3) {
      errors.linktreeUrl = "Please enter a valid Linktree URL or username";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleExtract = async () => {
    setIsLoading(true);
    setError(null);
    setApiResponse(null);
    try {
      const response = await fetch("/api/linktree", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ url: linktreeUrl }),
      });

      if (!response.ok) {
        throw new Error("Failed to extract data");
      }

      const data = await response.json();
      // Handle FireCrawl response structure
      let extractedData;

      if (data.success && data.data && data.data.length > 0) {
        // If we have successful data, use the first item
        extractedData = data.data[0];
      } else if (data.data && data.data.length > 0) {
        // Fallback to first data item
        extractedData = data.data[0];
      } else if (data.success === false && data.error) {
        // Handle error case
        extractedData = { error: data.error };
      } else {
        // Show the full response for debugging
        extractedData = data;
      }

      setAdditionalInfo(JSON.stringify(extractedData, null, 2));
      setApiResponse(data);
      toast({
        title: "Success",
        description: "Linktree data extracted successfully.",
      });
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    // Clear previous errors
    setError(null);
    setValidationErrors({});

    // Validate form before submission
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors before submitting.",
      });
      return;
    }

    if (autoExtract) {
      await handleExtract();
    }

    setIsLoading(true);
    try {
      const response = await fetch(
        "https://side-projects-linktree-extractor.vhhb1z.easypanel.host/api/v1/scrape/process",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            user: linktreeUrl.trim(),
            info: additionalInfo,
            template_name: selectedTemplate,
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to submit to external API");
      }

      toast({
        title: "Success",
        description: `Data submitted successfully using ${
          templateOptions.find((t) => t.value === selectedTemplate)?.label
        } template.`,
      });
      console.log(
        "Successfully submitted to external API with template:",
        selectedTemplate
      );
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 md:p-8">
      <Toaster />
      <h1 className="text-2xl sm:text-3xl font-bold mb-4">
        Linktree Extractor
      </h1>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <Form className="space-y-6">
                <FormField>
                  <FormLabel htmlFor="linktree-url" required>
                    Linktree URL/Username
                  </FormLabel>
                  <Input
                    id="linktree-url"
                    value={linktreeUrl}
                    onChange={(e) => {
                      setLinktreeUrl(e.target.value);
                      // Clear validation error when user starts typing
                      if (validationErrors.linktreeUrl) {
                        setValidationErrors((prev) => ({
                          ...prev,
                          linktreeUrl: "",
                        }));
                      }
                    }}
                    placeholder="e.g., https://linktr.ee/yourname or yourname"
                    error={validationErrors.linktreeUrl}
                  />
                  {validationErrors.linktreeUrl && (
                    <FormMessage>{validationErrors.linktreeUrl}</FormMessage>
                  )}
                  <FormDescription>
                    Enter the full Linktree URL or just the username
                  </FormDescription>
                </FormField>

                <FormField>
                  <FormLabel htmlFor="template-select">
                    Processing Template
                  </FormLabel>
                  <Select
                    value={selectedTemplate}
                    onValueChange={setSelectedTemplate}
                  >
                    <SelectTrigger id="template-select">
                      <SelectValue placeholder="Select a template" />
                    </SelectTrigger>
                    <SelectContent>
                      {templateOptions.map((template) => (
                        <SelectItem key={template.value} value={template.value}>
                          <div className="flex flex-col">
                            <span className="font-medium">
                              {template.label}
                            </span>
                            <span className="text-sm text-muted-foreground">
                              {template.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose the template that best matches your business type for
                    optimized AI processing
                  </FormDescription>
                </FormField>

                <FormField>
                  <FormLabel htmlFor="additional-info">
                    Additional Information
                  </FormLabel>
                  <Textarea
                    id="additional-info"
                    value={additionalInfo}
                    onChange={(e) => setAdditionalInfo(e.target.value)}
                    placeholder="Optional additional information about your business for better processing"
                    rows={8}
                    className="resize-none"
                  />
                  <FormDescription>
                    Provide any additional context about your business to
                    improve data processing
                  </FormDescription>
                </FormField>
                <FormField>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="auto-extract"
                      checked={autoExtract}
                      onCheckedChange={setAutoExtract}
                    />
                    <FormLabel htmlFor="auto-extract">
                      Automatically extract data before submitting
                    </FormLabel>
                  </div>
                  <FormDescription>
                    When enabled, the system will first extract data from the
                    Linktree page before processing
                  </FormDescription>
                </FormField>

                {error && <FormMessage type="error">{error}</FormMessage>}

                <div className="flex flex-col sm:flex-row gap-3 pt-4">
                  <Button
                    onClick={handleExtract}
                    disabled={isLoading || !linktreeUrl.trim()}
                    variant="outline"
                    className="flex-1"
                  >
                    {isLoading ? <LoadingSpinner /> : "Extract Info"}
                  </Button>
                  <Button
                    onClick={handleSubmit}
                    disabled={isLoading}
                    className="flex-1"
                  >
                    {isLoading ? <LoadingSpinner /> : "Process & Submit"}
                  </Button>
                </div>

                <Button
                  onClick={() =>
                    setAdditionalInfo(
                      "URL: | \nUSERNAME: | \nDESCRIÇÃO/BIO: | \nLOCALIZAÇÃO: | \nINSTAGRAM: | \nEQUIPE: | \nOGIMAGE: | \nBGIMAGE: \nREVIEW 1: | \nREVIEW 2: | \nREVIEW 3: | \nREVIEW 4: | \nREVIEW 5: |"
                    )
                  }
                  disabled={isLoading}
                  variant="ghost"
                  size="sm"
                  className="w-full"
                >
                  Insert Pre-defined Template
                </Button>
              </Form>
            </CardContent>
          </Card>
        </div>
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                API Response
                {selectedTemplate !== "general" && (
                  <span className="text-sm font-normal text-muted-foreground">
                    (
                    {
                      templateOptions.find((t) => t.value === selectedTemplate)
                        ?.label
                    }{" "}
                    Template)
                  </span>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading && (
                <div className="flex flex-col justify-center items-center py-8 space-y-3">
                  <LoadingSpinner />
                  <p className="text-sm text-muted-foreground">
                    Processing your Linktree data...
                  </p>
                </div>
              )}
              {apiResponse && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Response Data:</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        navigator.clipboard.writeText(
                          JSON.stringify(apiResponse, null, 2)
                        )
                      }
                    >
                      Copy JSON
                    </Button>
                  </div>
                  <pre className="p-4 bg-muted rounded-md overflow-x-auto text-sm border max-h-96 overflow-y-auto">
                    {JSON.stringify(apiResponse, null, 2)}
                  </pre>
                </div>
              )}
              {!isLoading && !apiResponse && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-2">No data yet</p>
                  <p className="text-sm text-muted-foreground">
                    The API response will be displayed here after processing.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

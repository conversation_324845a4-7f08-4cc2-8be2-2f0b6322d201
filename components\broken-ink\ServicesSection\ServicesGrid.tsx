import React from "react";
import { getIconComponent } from "@/lib/iconUtils";
import { ServiceCard } from "./ServiceCard";
import { Service } from "./types";
import { Scissors } from "lucide-react";

interface ServicesGridProps {
  services: Service[];
}

export const ServicesGrid: React.FC<ServicesGridProps> = ({ services }) => {
  return (
    <div className="flex flex-wrap items-center justify-center gap-6 mx-auto">
      {services.map((service) => {
        // Use icon from service data if available, otherwise use default Scissors icon
        const icon = service.iconName ? (
          (() => {
            const IconComponent = getIconComponent(service.iconName);
            return (
              <IconComponent
                className="h-8 w-8"
                role="img"
                aria-label={service.title + " icon"}
              />
            );
          })()
        ) : (
          <Scissors className="h-8 w-8" />
        );

        return (
          <ServiceCard
            key={service.title}
            icon={icon}
            title={service.title}
            description={service.description}
          />
        );
      })}
    </div>
  );
};

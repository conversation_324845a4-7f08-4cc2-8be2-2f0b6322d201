import { Button } from "@/components/ui/button";
import useEmblaCarousel from "embla-carousel-react";
import { useCallback, useEffect, useState } from "react";
import { ChevronLeft, ChevronRight, Phone } from "lucide-react";
import Image from "next/image";

interface SectionItem {
  id: number;
  title: string;
  description: string;
  image: string;
  primaryButton: {
    icon: string;
    url: string;
  };
  secondaryButton: {
    icon: string;
    url: string;
  };
}

interface SectionData {
  title: string;
  description: string;
  enabled: boolean;
  items: SectionItem[];
}

interface GenericSectionProps {
  sectionData: SectionData;
  layout?: "grid" | "carousel";
  primaryButtonText?: string;
  secondaryButtonText?: string;
  showBadge?: boolean;
  sectionType?: "features" | "services" | "generic";
  colors?: {
    background?: string;
    linkText?: string;
    primary?: string;
    secondary?: string;
    socialIconBackground?: string;
  };
}

const GenericSection = ({
  sectionData,
  layout = "grid",
  primaryButtonText = "Ver Mais",
  secondaryButtonText = "Contato",
  showBadge = false,
  sectionType = "generic",
  colors,
}: GenericSectionProps) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    containScroll: "trimSnaps",
    dragFree: false,
    loop: false,
    skipSnaps: false,
    slidesToScroll: "auto",
  });

  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);

    // Set loading to false after carousel is initialized
    const timer = setTimeout(() => setIsLoading(false), 100);

    // Cleanup both timer and Embla listeners to prevent leaks/duplicates
    return () => {
      clearTimeout(timer);
      try {
        emblaApi.off("select", onSelect);
        emblaApi.off("reInit", onSelect);
      } catch {
        // no-op if emblaApi instance was disposed
      }
    };
  }, [emblaApi, onSelect]);

  if (!sectionData.enabled) return null;

  const handleButtonClick = (url: string, event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (url && url !== "#" && url.startsWith("https")) {
      window.open(url, "_blank", "noopener,noreferrer");
    }
  };

  const renderCard = (item: SectionItem) => {
    const hasPrimaryButton =
      item.primaryButton.url && item.primaryButton.url !== "#";
    const hasSecondaryButton =
      item.secondaryButton.url && item.secondaryButton.url !== "#";

    // Create dynamic styles using API colors
    const primaryButtonStyle = colors?.primary
      ? ({
          backgroundColor: `${colors.primary}99`, // 60% opacity
          color: colors.linkText || "#ffffff",
          "--hover-bg": colors.primary,
        } as React.CSSProperties)
      : {};

    const secondaryButtonStyle = colors?.secondary
      ? ({
          backgroundColor: `${colors.secondary}1A`, // 10% opacity
          borderColor: `${colors.secondary}4D`, // 30% opacity
          color: colors.linkText || "#ffffff",
          "--hover-bg": `${colors.secondary}33`, // 20% opacity
          "--hover-border": `${colors.secondary}80`, // 50% opacity
        } as React.CSSProperties)
      : {};

    const badgeStyle = colors?.primary
      ? ({
          backgroundColor: `${colors.primary}E6`, // 90% opacity
        } as React.CSSProperties)
      : {};

    return (
      <div
        key={item.id}
        className="relative overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 transform-gpu group rounded-xl h-96 sm:h-80 lg:h-96"
      >
        <Image
          src={item.image}
          alt={item.title}
          fill
          className="object-cover transition-transform duration-700 group-hover:scale-110"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          priority={false}
        />
        {/* Enhanced gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent"></div>

        {/* Optional badge for services */}
        {showBadge && (
          <div className="absolute top-6 right-6">
            <div
              className="w-10 h-10 backdrop-blur-sm rounded-full flex items-center justify-center transform transition-all duration-300 group-hover:scale-110 shadow-lg"
              style={
                colors?.primary
                  ? badgeStyle
                  : { backgroundColor: "rgba(59, 130, 246, 0.9)" }
              }
            >
              <span className="text-white text-base font-bold">{item.id}</span>
            </div>
          </div>
        )}

        {/* Content positioned at bottom like Flutter version */}
        <div className="absolute bottom-0 left-0 right-0 p-6 sm:p-8">
          <h3 className="text-white font-bold text-xl sm:text-2xl mb-2 drop-shadow-lg">
            {item.title}
          </h3>
          <p className="text-white/90 text-sm sm:text-base mb-4 leading-relaxed drop-shadow-sm line-clamp-3">
            {item.description}
          </p>

          <div className="flex gap-3">
            {hasPrimaryButton && (
              <Button
                variant="default"
                size="lg"
                className="flex-1 h-14 backdrop-blur-sm hover:scale-105 transform transition-all duration-300 shadow-lg hover:shadow-xl group/btn border-0 rounded-2xl"
                style={
                  colors?.primary
                    ? primaryButtonStyle
                    : {
                        backgroundColor: "rgba(59, 130, 246, 0.6)",
                        color: "#ffffff",
                      }
                }
                onClick={(e) => handleButtonClick(item.primaryButton.url, e)}
                onMouseEnter={(e) => {
                  if (colors?.primary) {
                    e.currentTarget.style.backgroundColor = colors.primary;
                  }
                }}
                onMouseLeave={(e) => {
                  if (colors?.primary) {
                    e.currentTarget.style.backgroundColor = `${colors.primary}99`;
                  }
                }}
              >
                {primaryButtonText}
              </Button>
            )}
            {hasSecondaryButton && (
              <Button
                variant="outline"
                size="lg"
                className={`${
                  hasPrimaryButton ? "w-14 h-14 p-0" : "flex-1"
                } backdrop-blur-sm hover:scale-105 transform transition-all duration-300 group/btn shadow-lg rounded-2xl`}
                style={
                  colors?.secondary
                    ? secondaryButtonStyle
                    : {
                        backgroundColor: "rgba(255, 255, 255, 0.1)",
                        borderColor: "rgba(255, 255, 255, 0.3)",
                        color: "#ffffff",
                      }
                }
                onClick={(e) => handleButtonClick(item.secondaryButton.url, e)}
                onMouseEnter={(e) => {
                  if (colors?.secondary) {
                    e.currentTarget.style.backgroundColor = `${colors.secondary}33`;
                    e.currentTarget.style.borderColor = `${colors.secondary}80`;
                  }
                }}
                onMouseLeave={(e) => {
                  if (colors?.secondary) {
                    e.currentTarget.style.backgroundColor = `${colors.secondary}1A`;
                    e.currentTarget.style.borderColor = `${colors.secondary}4D`;
                  }
                }}
              >
                {item.secondaryButton.icon ? (
                  <i
                    className={`${item.secondaryButton.icon} ${
                      hasPrimaryButton ? "text-lg" : "mr-2"
                    } group-hover/btn:scale-110 transition-transform duration-300`}
                    aria-hidden="true"
                  />
                ) : (
                  <Phone
                    className={`${
                      hasPrimaryButton ? "w-5 h-5" : "w-4 h-4 mr-2"
                    }`}
                  />
                )}
                {!hasPrimaryButton && secondaryButtonText}
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderGridLayout = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {sectionData.items.map((item, index) => (
        <div
          key={item.id}
          className="animate-fade-in"
          style={{ animationDelay: `${index * 0.15}s` }}
        >
          {renderCard(item)}
        </div>
      ))}
    </div>
  );

  const renderCarouselLayout = () => (
    <div className="relative">
      <div
        className={`overflow-hidden rounded-2xl transition-opacity duration-300${
          isLoading ? "opacity-0" : "opacity-100"
        }`}
        ref={emblaRef}
      >
        <div className="flex gap-4 lg:gap-6">
          {sectionData.items.map((item, index) => (
            <div
              key={item.id}
              className="flex-[0_0_90%] sm:flex-[0_0_85%] md:flex-[0_0_80%] lg:flex-[0_0_360px] min-w-0"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {renderCard(item)}
            </div>
          ))}
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-center mt-8 gap-3">
        <Button
          variant="outline"
          size="sm"
          onClick={scrollPrev}
          disabled={!canScrollPrev}
          className="w-12 h-12 rounded-full p-0 backdrop-blur-sm transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform"
          style={
            colors?.primary
              ? {
                  backgroundColor: `${colors.primary}CC`, // 80% opacity
                  color: colors.linkText || "#ffffff",
                  borderColor: "transparent",
                }
              : {
                  backgroundColor: "rgba(59, 130, 246, 0.8)",
                  color: "#ffffff",
                  borderColor: "transparent",
                }
          }
          onMouseEnter={(e) => {
            if (colors?.primary && !e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = colors.primary;
            }
          }}
          onMouseLeave={(e) => {
            if (colors?.primary && !e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = `${colors.primary}CC`;
            }
          }}
          aria-label={`${sectionType} anterior`}
        >
          <ChevronLeft className="w-5 h-5" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={scrollNext}
          disabled={!canScrollNext}
          className="w-12 h-12 rounded-full p-0 backdrop-blur-sm transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform"
          style={
            colors?.primary
              ? {
                  backgroundColor: `${colors.primary}CC`, // 80% opacity
                  color: colors.linkText || "#ffffff",
                  borderColor: "transparent",
                }
              : {
                  backgroundColor: "rgba(59, 130, 246, 0.8)",
                  color: "#ffffff",
                  borderColor: "transparent",
                }
          }
          onMouseEnter={(e) => {
            if (colors?.primary && !e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = colors.primary;
            }
          }}
          onMouseLeave={(e) => {
            if (colors?.primary && !e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = `${colors.primary}CC`;
            }
          }}
          aria-label={`Próximo ${sectionType}`}
        >
          <ChevronRight className="w-5 h-5" />
        </Button>
      </div>

      {/* Progress indicator with active state like Flutter */}
      <div className="flex justify-center mt-4 gap-2">
        {sectionData.items.map((_, index) => (
          <div
            key={index}
            className="w-2 h-2 rounded-full transition-all duration-300 cursor-pointer"
            style={{
              backgroundColor: colors?.primary || "#3b82f6",
              opacity: selectedIndex === index ? 1 : 0.4,
              transform: selectedIndex === index ? "scale(1.25)" : "scale(1)",
            }}
            onMouseEnter={(e) => {
              if (selectedIndex !== index) {
                e.currentTarget.style.opacity = "0.6";
              }
            }}
            onMouseLeave={(e) => {
              if (selectedIndex !== index) {
                e.currentTarget.style.opacity = "0.4";
              }
            }}
            onClick={() => emblaApi?.scrollTo(index)}
          />
        ))}
      </div>
    </div>
  );

  return (
    <section className="mb-12 animate-fade-in">
      <div className="text-center mb-8 lg:mb-12">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground mb-3">
          {sectionData.title}
        </h2>
        <p className="text-muted-foreground max-w-md lg:max-w-2xl mx-auto text-sm sm:text-base leading-relaxed">
          {sectionData.description}
        </p>
      </div>

      {layout === "grid" ? renderGridLayout() : renderCarouselLayout()}
    </section>
  );
};

export default GenericSection;

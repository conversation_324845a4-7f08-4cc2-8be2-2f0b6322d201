/* ============================================================================
   TYPES INDEX - Central export for all type definitions
   ============================================================================ */

// Import types for runtime use in functions (kept as type-only to avoid runtime cost)
import type {
  UserProfile,
  User,
  Link,
  SocialMediaLink,
  SectionItem,
  Review,
  GalleryImage,
} from './user'

import {
  ICON_CLASSES,
  DEFAULT_COLOR_SCHEMES,
  PLACEHOLDER_IMAGES,
  DEFAULT_SECTION_VALUES,
} from './constants'

// Core user and profile types
export type {
  // Main interfaces
  UserProfile,
  User,
  Link,
  SocialMediaLink,
  But<PERSON>,
  ButtonConfig,

  // Section interfaces
  FeaturesSection,
  ServicesSection,
  GenericSection,
  TeamSection,
  TeamMember,
  SectionItem,

  // Gallery interfaces
  Gallery,
  GalleryImage,

  // Reviews interfaces
  ReviewsSection,
  Review,

  // Video interface
  VideoSection,

  // Location interface
  LocationData,

  // Settings interfaces
  Settings,
  ColorSettings,

  // API interfaces
  ApiResponse,
  UserProfileResponse,

  // Legacy interfaces (deprecated)
  UserLink,
  CreateUserRequest,
  UpdateUserRequest,
  CreateLinkRequest,
  UpdateLinkRequest,
  UserStats,
  ApiError,
} from './user'

// Constants and enums
export {
  ICON_CLASSES,
  DEFAULT_COLOR_SCHEMES,
  RATING_VALUES,
  SECTION_TYPES,
  BUTTON_TYPES,
  URL_PATTERNS,
  PLACEHOLDER_IMAGES,
  DEFAULT_SECTION_VALUES,
  SECTION_BUTTON_CONFIG,
} from './constants'

export type {
  IconClass,
  ColorScheme,
  RatingValue,
  SectionType,
  ButtonType,
} from './constants'

// Utility types
export type {
  // Generic utility types
  DeepPartial,
  RequiredFields,
  OptionalFields,
  ArrayElement,
  PickByType,
  OmitByType,
  ValueOf,
  NonEmptyArray,
  StringLiteral,
  NumericLiteral,
  NonNullable,
  Parameters,
  ReturnType,

  // Form and validation types
  FormField,
  ValidationResult,
  LoadingState,
  AsyncState,
  PaginationState,
  SortState,
  FilterState,

  // Event and handler types
  EventHandler,
  ChangeHandler,
  ClickHandler,
  SubmitHandler,
  AsyncEventHandler,

  // Component prop types
  BaseComponentProps,
  WithChildren,
  WithOptionalChildren,
  WithLoading,
  WithError,
  WithDisabled,
  WithVariant,
  WithSize,

  // API and data types
  ApiResponseWrapper,
  ErrorResponse,
  SuccessResponse,
  FailedResponse,
  ApiResult,
  ListMetadata,
  PaginatedResponse,
} from './utils'

// Validation types and functions
export type {
  ValidationError,
  ValidationResult as ValidationResultType,
} from './validation'

export {
  // Validation functions
  isValidUrl,
  isValidEmail,
  isValidPhone,
  isValidUsername,
  isValidHexColor,
  isValidRating,
  isValidIconClass,

  // Object validation functions
  validateUser,
  validateLink,
  validateSectionItem,
  validateReview,
  validateGalleryImage,
  validateColorSettings,
  validateButtonConfig,
  validateLocationData,
  validateUserProfile,

  // Sanitization functions
  sanitizeString,
  sanitizeUrl,
} from './validation'

// Error classes
export {
  UserNotFoundError,
  InvalidUsernameError,
} from './user'

// ============================================================================
// TYPE GUARDS
// ============================================================================

/**
 * Type guard to check if a value is a valid UserProfile
 */
export function isUserProfile(value: unknown): value is UserProfile {
  if (!value || typeof value !== 'object') return false
  const v: Record<string, unknown> = value as Record<string, unknown>
  const user = v.user as Record<string, unknown> | undefined
  const settings = v.settings as Record<string, unknown> | undefined

  // user
  if (!user || typeof user !== 'object') return false
  if (typeof user.username !== 'string') return false
  if (typeof user.name !== 'string') return false
  if (typeof user.bio !== 'string') return false
  if (typeof user.avatar !== 'string') return false
  if (typeof user.heroImage !== 'string') return false
  if (typeof user.brandLogo !== 'string') return false

  // arrays
  if (!Array.isArray(v.links)) return false
  if (!Array.isArray(v.socialMedia)) return false

  // settings
  if (!settings || typeof settings !== 'object') return false
  if (typeof settings.favicon !== 'string') return false
  if (typeof settings.pageDescription !== 'string') return false
  if (typeof settings.pageKeywords !== 'string') return false
  if (typeof settings.ogImage !== 'string') return false
  if (typeof settings.backgroundVideo !== 'string') return false

  const colors = (settings as Record<string, unknown>).colors as Record<string, unknown> | undefined
  if (!colors || typeof colors !== 'object') return false

  return true
}

/**
 * Type guard to check if a value is a valid Link
 */
export function isLink(value: unknown): value is Link {
  if (!value || typeof value !== 'object') return false
  const v = value as Record<string, unknown>
  return (
    typeof v.text === 'string' &&
    typeof v.url === 'string' &&
    typeof v.classIcon === 'string'
  )
}

/**
 * Type guard to check if a value is a valid SectionItem
 */
export function isSectionItem(value: unknown): value is SectionItem {
  if (!value || typeof value !== 'object') return false
  const v = value as Record<string, unknown>
  if (typeof v.id !== 'number') return false
  if (typeof v.title !== 'string') return false
  if (typeof v.description !== 'string') return false
  // image is optional in the interface; if present ensure string
  if (typeof v.image !== 'undefined' && typeof v.image !== 'string') return false

  const primary = v.primaryButton as Record<string, unknown> | undefined
  const secondary = v.secondaryButton as Record<string, unknown> | undefined
  if (!primary || typeof primary !== 'object') return false
  if (!secondary || typeof secondary !== 'object') return false
  if (typeof primary.icon !== 'string' || typeof primary.url !== 'string') return false
  if (typeof secondary.icon !== 'string' || typeof secondary.url !== 'string') return false

  return true
}

/**
 * Type guard to check if a value is a valid Review
 */
export function isReview(value: unknown): value is Review {
  if (!value || typeof value !== 'object') return false
  const v = value as Record<string, unknown>
  return (
    typeof v.id === 'number' &&
    typeof v.name === 'string' &&
    typeof v.comment === 'string' &&
    typeof v.photo === 'string' &&
    typeof v.rating === 'number' &&
    v.rating >= 1 &&
    v.rating <= 5
  )
}

/**
 * Type guard to check if a value is a valid GalleryImage
 */
export function isGalleryImage(value: unknown): value is GalleryImage {
  if (!value || typeof value !== 'object') return false
  const v = value as Record<string, unknown>
  return (
    typeof v.id === 'number' &&
    typeof v.title === 'string' &&
    typeof v.alt === 'string' &&
    typeof v.url === 'string' &&
    typeof v.description === 'string'
  )
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Creates a default UserProfile object
 */
export function createDefaultUserProfile(username: string, name: string): UserProfile {
  return {
    user: {
      username,
      name,
      bio: '',
      avatar: PLACEHOLDER_IMAGES.USER_AVATAR,
      heroImage: '',
      brandLogo: '',
    },
    links: [],
    socialMedia: [],
    phone: '',
    settings: {
      colors: DEFAULT_COLOR_SCHEMES.LIGHT,
      favicon: '',
      pageDescription: '',
      pageKeywords: '',
      ogImage: '',
      backgroundVideo: '',
    },
    featuresSection: {
      title: '',
      description: '',
      enabled: false,
      items: [],
    },
    servicesSection: {
      title: '',
      description: '',
      enabled: false,
      items: [],
    },
    genericSection: {
      title: '',
      description: '',
      enabled: false,
      items: [],
    },
    gallery: {
      title: '',
      description: '',
      enabled: false,
      images: [],
    },
    reviews: {
      title: '',
      description: '',
      enabled: false,
      reviews: [],
    },
    video: {
      title: '',
      description: '',
      enabled: false,
      youtubeUrl: '',
    },
    location: {
      enabled: false,
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: '',
      },
      contact: {
        phone: '',
        whatsapp: '',
      },
      hours: {
        weekdays: '',
        weekends: '',
      },
      googleMapsUrl: '',
    },
    team: {
      enabled: false,
      title: '',
      members: [],
    },
  }
}

/**
 * Creates a default Link object
 */
export function createDefaultLink(text: string, url: string, icon: string = ICON_CLASSES.PLACEHOLDER): Link {
  return {
    text,
    url,
    classIcon: icon,
  }
}

/**
 * Creates a default SectionItem object
 */
export function createDefaultSectionItem(id: number, title: string): SectionItem {
  return {
    id,
    title,
    description: '',
    image: PLACEHOLDER_IMAGES.GALLERY_IMAGE,
    primaryButton: {
      icon: ICON_CLASSES.PLACEHOLDER,
      url: DEFAULT_SECTION_VALUES.EMPTY_URL,
    },
    secondaryButton: {
      icon: ICON_CLASSES.PLACEHOLDER,
      url: DEFAULT_SECTION_VALUES.EMPTY_URL,
    },
  }
}
